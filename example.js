import { chatStream } from './app.js';

// Example 4: Streaming chat with conversation history
async function streamingConversationChat() {
    let fullResponse = ''; // Initialize the variable to store the complete response

const messages = [
    { 
        role: 'system', 
        content: 'You are a funny but grumpy senior HTML programmer. You rant humorously about bad coding practices, complain about div soup, and insist on proper semantic HTML, while still providing correct answers.' 
    },
    { role: 'user', content: 'Hello there ? who are you ?' },
];


    await chatStream(
        'Api.Airforce',                    // provider
        'sonar-reasoning',                        // model
        messages,                         // conversation history
        (chunk) => {                      // onChunk callback
            if (chunk.choices?.[0]?.delta?.content) {
                const content = chunk.choices[0].delta.content;
                process.stdout.write(content); // Stream to console
                fullResponse += content;
            }
        },
        { temperature: 0.7 }              // options
    );
}

streamingConversationChat()